#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - 生产环境版本
优化性能和安全性
"""

import os
import logging
import json
import secrets
from datetime import datetime, timedelta
from functools import wraps
from flask import Flask, request, jsonify, send_from_directory, session
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv
import hashlib
import hmac
from werkzeug.middleware.proxy_fix import ProxyFix

# 加载环境变量
load_dotenv()

# 配置日志
log_level = os.getenv('LOG_LEVEL', 'INFO')
logging.basicConfig(
    level=getattr(logging, log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 代理配置（用于生产环境）
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

# 安全配置
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_hex(32))
app.config['SESSION_COOKIE_SECURE'] = os.getenv('FLASK_ENV') == 'production'
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置CORS
allowed_origins = os.getenv('ALLOWED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:5000').split(',')
CORS(app, origins=allowed_origins, supports_credentials=True)

# 配置限流器
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=[os.getenv('RATE_LIMIT_DEFAULT', '200 per day'), "50 per hour"],
    storage_uri="memory://"
)

# 数据库连接池配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'root'),
    'database': os.getenv('DB_NAME', 'nis_security_news'),
    'charset': 'utf8mb4',
    'autocommit': True,
    'use_unicode': True,
    'pool_name': 'nis_pool',
    'pool_size': 10,
    'pool_reset_session': True,
    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
}

# 管理员配置
ADMIN_CONFIG = {
    'username': os.getenv('ADMIN_USERNAME', 'admin'),
    'password_hash': hashlib.sha256(os.getenv('ADMIN_PASSWORD', 'nis2024').encode()).hexdigest()
}

# 缓存配置
CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', '300'))  # 5分钟缓存

# 简单内存缓存
cache = {}
cache_timestamps = {}

def get_from_cache(key):
    """从缓存获取数据"""
    if key in cache and key in cache_timestamps:
        if datetime.now().timestamp() - cache_timestamps[key] < CACHE_TIMEOUT:
            return cache[key]
        else:
            # 缓存过期，清理
            del cache[key]
            del cache_timestamps[key]
    return None

def set_cache(key, value):
    """设置缓存"""
    cache[key] = value
    cache_timestamps[key] = datetime.now().timestamp()

def clear_cache():
    """清理所有缓存"""
    cache.clear()
    cache_timestamps.clear()

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def execute_query(query, params=None, fetch_one=False, use_cache=False, cache_key=None):
    """执行数据库查询 - 生产版本，支持缓存"""
    
    # 如果启用缓存且是SELECT查询
    if use_cache and cache_key and query.strip().upper().startswith('SELECT'):
        cached_result = get_from_cache(cache_key)
        if cached_result is not None:
            return cached_result
    
    connection = None
    cursor = None
    try:
        connection = get_db_connection()
        if not connection:
            return None
            
        cursor = connection.cursor(dictionary=True, buffered=True)
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            if fetch_one:
                result = cursor.fetchone()
            else:
                result = cursor.fetchall()
            
            # 缓存SELECT查询结果
            if use_cache and cache_key and result:
                set_cache(cache_key, result)
        else:
            connection.commit()
            result = cursor.rowcount
            
            # 清理相关缓存
            if cache_key:
                clear_cache()
        
        return result
        
    except Error as e:
        logger.error(f"数据库操作失败: {e}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def validate_input(data, required_fields):
    """验证输入数据"""
    if not isinstance(data, dict):
        return False, "无效的数据格式"
    
    for field in required_fields:
        if field not in data or not data[field]:
            return False, f"缺少必填字段: {field}"
    
    return True, "验证通过"

def sanitize_string(text, max_length=1000):
    """清理字符串输入"""
    if not isinstance(text, str):
        return ""
    
    # 移除潜在的恶意字符
    text = text.strip()
    text = text.replace('<script>', '').replace('</script>', '')
    text = text.replace('javascript:', '')
    text = text.replace('data:', '')
    text = text.replace('vbscript:', '')
    
    # 限制长度
    if len(text) > max_length:
        text = text[:max_length]
    
    return text

def require_admin_auth(f):
    """管理员认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session or not session['admin_logged_in']:
            return jsonify({'success': False, 'message': '需要管理员权限'}), 401
        return f(*args, **kwargs)
    return decorated_function

def generate_csrf_token():
    """生成CSRF令牌"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(32)
    return session['csrf_token']

def validate_csrf_token(token):
    """验证CSRF令牌"""
    return 'csrf_token' in session and hmac.compare_digest(session['csrf_token'], token)

def log_admin_action(action, table_name=None, record_id=None, old_values=None, new_values=None):
    """记录管理员操作日志"""
    try:
        user_id = session.get('admin_username', 'unknown')
        ip_address = get_remote_address()
        user_agent = request.headers.get('User-Agent', '')
        
        execute_query("""
            INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (user_id, action, table_name, record_id, 
              json.dumps(old_values) if old_values else None,
              json.dumps(new_values) if new_values else None,
              ip_address, user_agent))
    except Exception as e:
        logger.error(f"记录操作日志失败: {e}")

# ==================== 静态文件路由 ====================

@app.route('/')
def root():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """安全的静态文件服务"""
    # 防止目录遍历攻击
    if '..' in filename or filename.startswith('/'):
        return jsonify({'error': '非法的文件路径'}), 400
    
    # 只允许特定的文件扩展名
    allowed_extensions = {'.html', '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.pdf', '.txt'}
    file_ext = os.path.splitext(filename)[1].lower()
    
    if file_ext not in allowed_extensions:
        return jsonify({'error': '不支持的文件类型'}), 400
    
    try:
        return send_from_directory('.', filename)
    except FileNotFoundError:
        return jsonify({'error': '文件不存在'}), 404

# ==================== API路由 ====================

@app.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """获取CSRF令牌"""
    return jsonify({'csrf_token': generate_csrf_token()})

@app.route('/api/announcements', methods=['GET'])
@limiter.limit("30/minute")
def get_announcements():
    """获取公告列表"""
    try:
        cache_key = "announcements_list"
        announcements = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            WHERE status = 'published' AND publish_time <= NOW()
            AND (expire_time IS NULL OR expire_time > NOW())
            ORDER BY is_pinned DESC, publish_time DESC
            LIMIT 50
        """, use_cache=True, cache_key=cache_key)
        
        if not announcements:
            announcements = []
        
        return jsonify({'success': True, 'data': announcements})
    except Exception as e:
        logger.error(f"获取公告失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取公告失败'}), 500

@app.route('/api/announcements/<int:announcement_id>', methods=['GET'])
@limiter.limit("50/minute")
def get_announcement_detail(announcement_id):
    """获取公告详情"""
    try:
        # 验证ID范围
        if announcement_id <= 0 or announcement_id > 999999:
            return jsonify({'success': False, 'message': '无效的公告ID'}), 400
            
        cache_key = f"announcement_{announcement_id}"
        announcement = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            WHERE id = %s AND status = 'published' AND publish_time <= NOW()
            AND (expire_time IS NULL OR expire_time > NOW())
        """, (announcement_id,), fetch_one=True, use_cache=True, cache_key=cache_key)
        
        if not announcement:
            return jsonify({'success': False, 'message': '公告不存在'}), 404
        
        return jsonify({'success': True, 'data': announcement})
    except Exception as e:
        logger.error(f"获取公告详情失败: {e}")
        return jsonify({'success': False, 'message': '获取公告详情失败'}), 500

@app.route('/api/news-links', methods=['GET'])
@limiter.limit("30/minute")
def get_news_links():
    """获取新闻链接列表"""
    try:
        page = max(1, int(request.args.get('page', 1)))
        limit = min(50, max(1, int(request.args.get('limit', 10))))
        offset = (page - 1) * limit
        
        cache_key = f"news_links_page_{page}_limit_{limit}"
        links = execute_query("""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE nl.status = 'active'
            ORDER BY nl.is_featured DESC, nl.created_at DESC
            LIMIT %s OFFSET %s
        """, (limit, offset), use_cache=True, cache_key=cache_key)
        
        if not links:
            links = []
        
        return jsonify({'success': True, 'data': links})
    except ValueError:
        return jsonify({'success': False, 'message': '无效的分页参数'}), 400
    except Exception as e:
        logger.error(f"获取新闻链接失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取新闻链接失败'}), 500

@app.route('/api/categories', methods=['GET'])
@limiter.limit("30/minute")
def get_categories():
    """获取新闻分类"""
    try:
        cache_key = "news_categories"
        categories = execute_query("""
            SELECT id, name, description, sort_order
            FROM news_categories
            WHERE is_active = 1
            ORDER BY sort_order
            LIMIT 20
        """, use_cache=True, cache_key=cache_key)

        if not categories:
            categories = []

        return jsonify({'success': True, 'data': categories})
    except Exception as e:
        logger.error(f"获取分类失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取分类失败'}), 500

@app.route('/api/security-news', methods=['GET'])
@limiter.limit("30/minute")
def get_security_news():
    """获取安全新闻列表（兼容前端调用）"""
    try:
        page = max(1, int(request.args.get('page', 1)))
        limit = min(50, max(1, int(request.args.get('limit', 10))))
        category = sanitize_string(request.args.get('category', 'all'), 50)
        search = sanitize_string(request.args.get('search', ''), 100)

        # 构建查询条件
        where_conditions = ["nl.status = 'active'"]
        params = []

        if category != 'all' and category:
            where_conditions.append("nc.name = %s")
            params.append(category)

        if search:
            where_conditions.append("(nl.title LIKE %s OR nl.description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)
        offset = (page - 1) * limit

        # 构建缓存键
        cache_key = f"security_news_{page}_{limit}_{category}_{search}"

        # 查询新闻链接
        query = f"""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE {where_clause}
            ORDER BY nl.is_featured DESC, nl.created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([limit, offset])

        links = execute_query(query, params, use_cache=True, cache_key=cache_key)

        if not links:
            links = []

        # 检查是否还有更多数据
        count_query = f"""
            SELECT COUNT(*) as total
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE {where_clause}
        """
        count_result = execute_query(count_query, params[:-2], fetch_one=True)
        total = count_result['total'] if count_result else len(links)
        has_more = (page * limit) < total

        return jsonify({
            'success': True,
            'data': {
                'news': links,
                'has_more': has_more,
                'total': total,
                'page': page
            }
        })
    except ValueError:
        return jsonify({'success': False, 'message': '无效的分页参数'}), 400
    except Exception as e:
        logger.error(f"获取安全新闻失败: {e}")
        return jsonify({
            'success': False,
            'data': {
                'news': [],
                'has_more': False,
                'total': 0,
                'page': 1
            },
            'message': '获取安全新闻失败'
        }), 500

@app.route('/api/news-links/<int:link_id>/view', methods=['POST'])
@limiter.limit("20/minute")
def increment_view_count(link_id):
    """增加链接浏览次数"""
    try:
        # 验证ID范围
        if link_id <= 0 or link_id > 999999:
            return jsonify({'success': False, 'message': '无效的链接ID'}), 400
            
        result = execute_query("""
            UPDATE news_links
            SET views = views + 1
            WHERE id = %s AND status = 'active'
        """, (link_id,))

        if result and result > 0:
            # 清理相关缓存
            clear_cache()
            return jsonify({'success': True, 'message': '浏览次数已更新'})
        else:
            return jsonify({'success': False, 'message': '链接不存在'}), 404
    except Exception as e:
        logger.error(f"更新浏览次数失败: {e}")
        return jsonify({'success': False, 'message': '更新失败'}), 500

@app.route('/api/projects', methods=['GET'])
@limiter.limit("30/minute")
def get_projects():
    """获取项目列表"""
    try:
        cache_key = "projects_list"
        projects = execute_query("""
            SELECT id, title, description, github_url, demo_url, technologies as tags,
                   status, award, is_featured, created_at
            FROM projects
            WHERE status != 'archived'
            ORDER BY is_featured DESC, created_at DESC
            LIMIT 50
        """, use_cache=True, cache_key=cache_key)

        if not projects:
            projects = []
        else:
            # 处理JSON字段
            for project in projects:
                if project.get('tags') and isinstance(project['tags'], str):
                    try:
                        project['tags'] = json.loads(project['tags'])
                    except json.JSONDecodeError:
                        project['tags'] = []

        return jsonify({'success': True, 'data': projects})
    except Exception as e:
        logger.error(f"获取项目失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取项目失败'}), 500

@app.route('/api/team', methods=['GET'])
@limiter.limit("30/minute")
def get_team():
    """获取团队成员"""
    try:
        cache_key = "team_members"
        team = execute_query("""
            SELECT id, name, position as role, bio as achievement, skills,
                   github_url, email, join_date, is_core
            FROM team_members
            WHERE is_active = 1
            ORDER BY is_core DESC, sort_order ASC
            LIMIT 50
        """, use_cache=True, cache_key=cache_key)

        if not team:
            team = []
        else:
            # 处理JSON字段
            for member in team:
                if member.get('skills') and isinstance(member['skills'], str):
                    try:
                        member['skills'] = json.loads(member['skills'])
                    except json.JSONDecodeError:
                        member['skills'] = []

        return jsonify({'success': True, 'data': team})
    except Exception as e:
        logger.error(f"获取团队信息失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取团队信息失败'}), 500

@app.route('/api/resources', methods=['GET'])
@limiter.limit("30/minute")
def get_resources():
    """获取学习资源"""
    try:
        cache_key = "learning_resources"
        resources = execute_query("""
            SELECT id, title, description, url, resource_type as type,
                   category, difficulty, tags, download_count, is_featured, created_at
            FROM learning_resources
            WHERE status = 'active'
            ORDER BY is_featured DESC, created_at DESC
            LIMIT 50
        """, use_cache=True, cache_key=cache_key)

        if not resources:
            resources = []
        else:
            # 处理JSON字段
            for resource in resources:
                if resource.get('tags') and isinstance(resource['tags'], str):
                    try:
                        resource['tags'] = json.loads(resource['tags'])
                    except json.JSONDecodeError:
                        resource['tags'] = []

        return jsonify({'success': True, 'data': resources})
    except Exception as e:
        logger.error(f"获取学习资源失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取学习资源失败'}), 500

@app.route('/api/events', methods=['GET'])
@limiter.limit("30/minute")
def get_events():
    """获取活动列表"""
    try:
        cache_key = "events_list"
        events = execute_query("""
            SELECT id, title, description, content, event_date, location,
                   image_url, participants_count, status, is_featured, created_at
            FROM events
            ORDER BY event_date DESC
            LIMIT 50
        """, use_cache=True, cache_key=cache_key)

        if not events:
            events = []

        return jsonify({'success': True, 'data': events})
    except Exception as e:
        logger.error(f"获取活动失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取活动失败'}), 500

# ==================== 管理员API ====================

@app.route('/api/admin/login', methods=['POST'])
@limiter.limit("5/minute")
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400
            
        username = sanitize_string(data.get('username', ''), 50)
        password = data.get('password', '')
        csrf_token = data.get('csrf_token', '')

        # 验证CSRF令牌
        if not validate_csrf_token(csrf_token):
            return jsonify({'success': False, 'message': 'CSRF令牌无效'}), 403

        # 验证用户名和密码
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        if (username == ADMIN_CONFIG['username'] and 
            hmac.compare_digest(password_hash, ADMIN_CONFIG['password_hash'])):
            
            session['admin_logged_in'] = True
            session['admin_username'] = username
            session['login_time'] = datetime.now().isoformat()
            session.permanent = True
            
            # 记录登录日志
            log_admin_action('login')
            logger.info(f"管理员 {username} 登录成功，IP: {get_remote_address()}")
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'csrf_token': generate_csrf_token()
            })
        else:
            logger.warning(f"管理员登录失败，用户名: {username}，IP: {get_remote_address()}")
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'message': '登录失败'}), 500

@app.route('/api/admin/logout', methods=['POST'])
@require_admin_auth
def admin_logout():
    """管理员退出登录"""
    try:
        username = session.get('admin_username', 'unknown')
        log_admin_action('logout')
        session.clear()
        logger.info(f"管理员 {username} 退出登录")
        return jsonify({'success': True, 'message': '退出成功'})
    except Exception as e:
        logger.error(f"退出登录失败: {e}")
        return jsonify({'success': False, 'message': '退出失败'}), 500

# 其他管理员API路由保持不变...
# (为了节省空间，这里省略了其他管理员API的实现，实际使用时需要包含完整的管理员功能)

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'message': '页面不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"服务器内部错误: {error}")
    return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({'success': False, 'message': '请求过于频繁，请稍后再试'}), 429

@app.errorhandler(403)
def forbidden(error):
    return jsonify({'success': False, 'message': '访问被禁止'}), 403

@app.errorhandler(400)
def bad_request(error):
    return jsonify({'success': False, 'message': '请求格式错误'}), 400

# ==================== 健康检查 ====================

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        connection = get_db_connection()
        if connection:
            connection.close()
            db_status = "healthy"
        else:
            db_status = "unhealthy"
        
        return jsonify({
            'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
            'database': db_status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# ==================== 启动应用 ====================

if __name__ == '__main__':
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 环境检查
    env = os.getenv('FLASK_ENV', 'development')
    debug_mode = env == 'development'
    
    logger.info("🚀 NIS社团网站启动中...")
    logger.info(f"📊 生产环境版本 (环境: {env})")
    logger.info("🔒 已启用安全增强功能")
    logger.info("⚡ 已启用性能优化")
    logger.info("🌐 访问地址: http://0.0.0.0:5000")
    logger.info("🔧 管理后台: http://0.0.0.0:5000/admin_simple.html")
    logger.info("🔐 管理员账号: admin / nis2024")
    logger.info("💾 已启用数据库连接池")
    logger.info("🗄️ 已启用内存缓存")
    
    app.run(
        debug=debug_mode, 
        host='0.0.0.0', 
        port=int(os.getenv('PORT', 5000)), 
        threaded=True
    )

