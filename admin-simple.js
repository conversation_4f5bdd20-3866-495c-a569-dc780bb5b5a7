// 简化的管理员后台JavaScript
class AdminManager {
    constructor() {
        this.isLoggedIn = false;
        this.currentTab = 'announcements';
        this.editingId = null;
        this.apiBase = '/api';
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkLoginStatus();
    }

    bindEvents() {
        // 登录表单
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 退出登录
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        // 标签切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.target.closest('.nav-tab').dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 添加公告按钮
        const addAnnouncementBtn = document.getElementById('add-announcement-btn');
        if (addAnnouncementBtn) {
            addAnnouncementBtn.addEventListener('click', () => this.showAnnouncementModal());
        }

        // 添加链接按钮
        const addLinkBtn = document.getElementById('add-link-btn');
        if (addLinkBtn) {
            addLinkBtn.addEventListener('click', () => this.showLinkModal());
        }

        // 保存设置按钮
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (username === 'admin' && password === 'nis2024') {
            this.isLoggedIn = true;
            localStorage.setItem('adminLoggedIn', 'true');
            this.showAdminInterface();
            await this.loadData();
            this.showMessage('登录成功！', 'success');
        } else {
            this.showMessage('用户名或密码错误！', 'error');
        }
    }

    handleLogout() {
        this.isLoggedIn = false;
        localStorage.removeItem('adminLoggedIn');
        this.showLoginForm();
        this.showMessage('已退出登录', 'info');
    }

    checkLoginStatus() {
        if (localStorage.getItem('adminLoggedIn') === 'true') {
            this.isLoggedIn = true;
            this.showAdminInterface();
            this.loadData();
        } else {
            this.showLoginForm();
        }
    }

    showLoginForm() {
        document.getElementById('login-section').style.display = 'block';
        document.getElementById('admin-interface').style.display = 'none';
    }

    showAdminInterface() {
        document.getElementById('login-section').style.display = 'none';
        document.getElementById('admin-interface').style.display = 'block';
    }

    switchTab(tabName) {
        // 更新标签状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${tabName}-section`).classList.add('active');

        this.currentTab = tabName;
        this.loadData();
    }

    async loadData() {
        try {
            if (this.currentTab === 'announcements') {
                await this.loadAnnouncements();
            } else if (this.currentTab === 'news-links') {
                await this.loadNewsLinks();
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showMessage('加载数据失败', 'error');
        }
    }

    async loadAnnouncements() {
        try {
            const response = await fetch(`${this.apiBase}/announcements`);
            if (response.ok) {
                const announcements = await response.json();
                this.renderAnnouncementsTable(announcements);
            } else {
                // 如果API失败，显示示例数据
                this.renderAnnouncementsTable([
                    {
                        id: 1,
                        title: '欢迎使用管理系统',
                        type: 'notice',
                        status: 'published',
                        publish_time: '2024-07-16 10:00:00'
                    }
                ]);
            }
        } catch (error) {
            console.error('加载公告失败:', error);
            this.renderAnnouncementsTable([]);
        }
    }

    async loadNewsLinks() {
        try {
            const response = await fetch(`${this.apiBase}/news-links`);
            if (response.ok) {
                const links = await response.json();
                this.renderLinksTable(links);
            } else {
                // 如果API失败，显示示例数据
                this.renderLinksTable([
                    {
                        id: 1,
                        title: 'FreeBuf安全资讯',
                        category: '网络安全',
                        views: 156,
                        status: 'active'
                    }
                ]);
            }
        } catch (error) {
            console.error('加载链接失败:', error);
            this.renderLinksTable([]);
        }
    }

    renderAnnouncementsTable(announcements) {
        const tableBody = document.getElementById('announcements-table');
        if (!tableBody) return;

        if (announcements.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">暂无数据</td></tr>';
            return;
        }

        tableBody.innerHTML = announcements.map(announcement => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${announcement.title}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.getTypeLabel(announcement.type)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.getStatusLabel(announcement.status)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.formatDate(announcement.publish_time || announcement.created_at)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="adminManager.editAnnouncement(${announcement.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                    <button onclick="adminManager.deleteAnnouncement(${announcement.id})" class="text-red-600 hover:text-red-900">删除</button>
                </td>
            </tr>
        `).join('');
    }

    renderLinksTable(links) {
        const tableBody = document.getElementById('links-table');
        if (!tableBody) return;

        if (links.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">暂无数据</td></tr>';
            return;
        }

        tableBody.innerHTML = links.map(link => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${link.title}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${link.category || '未分类'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${link.views || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${this.getStatusLabel(link.status)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="adminManager.editLink(${link.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                    <button onclick="adminManager.deleteLink(${link.id})" class="text-red-600 hover:text-red-900">删除</button>
                </td>
            </tr>
        `).join('');
    }

    showAnnouncementModal(announcement = null) {
        this.showMessage('公告管理功能开发中...', 'info');
    }

    showLinkModal(link = null) {
        this.showMessage('链接管理功能开发中...', 'info');
    }

    async saveSettings() {
        const settings = {
            siteName: document.getElementById('site-name').value,
            adminEmail: document.getElementById('admin-email').value,
            contactQQ: document.getElementById('contact-qq').value,
            contactDouyin: document.getElementById('contact-douyin').value
        };

        this.showMessage('设置已保存（演示模式）', 'success');
    }

    editAnnouncement(id) {
        this.showMessage(`编辑公告 ID: ${id}（功能开发中）`, 'info');
    }

    deleteAnnouncement(id) {
        if (confirm('确定要删除这个公告吗？')) {
            this.showMessage(`删除公告 ID: ${id}（功能开发中）`, 'info');
        }
    }

    editLink(id) {
        this.showMessage(`编辑链接 ID: ${id}（功能开发中）`, 'info');
    }

    deleteLink(id) {
        if (confirm('确定要删除这个链接吗？')) {
            this.showMessage(`删除链接 ID: ${id}（功能开发中）`, 'info');
        }
    }

    getTypeLabel(type) {
        const types = {
            'notice': '通知',
            'event': '活动',
            'urgent': '紧急'
        };
        return types[type] || type;
    }

    getStatusLabel(status) {
        const statuses = {
            'published': '已发布',
            'draft': '草稿',
            'archived': '已归档',
            'active': '活跃',
            'inactive': '停用'
        };
        return statuses[status] || status;
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'warning' ? 'bg-yellow-500' :
            'bg-blue-500'
        }`;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
}

// 初始化管理器
let adminManager;
document.addEventListener('DOMContentLoaded', function() {
    adminManager = new AdminManager();
});
