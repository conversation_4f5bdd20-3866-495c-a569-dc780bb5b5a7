#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - 数据库设置脚本
使用MySQL root/root账户
"""

import mysql.connector
from mysql.connector import Error
import logging
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'root'),
    'charset': 'utf8mb4'
}

DATABASE_NAME = os.getenv('DB_NAME', 'nis_security_news')

def create_database():
    """创建数据库"""
    connection = None
    cursor = None
    try:
        # 连接MySQL服务器
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DATABASE_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        logger.info("✅ 数据库创建成功")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 创建数据库失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def create_tables():
    """创建数据表"""
    connection = None
    cursor = None
    try:
        # 连接到指定数据库
        db_config = DB_CONFIG.copy()
        db_config['database'] = DATABASE_NAME
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 创建表的SQL语句
        tables = {
            'announcements': """
                CREATE TABLE IF NOT EXISTS announcements (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    content TEXT,
                    is_pinned TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_created_at (created_at),
                    INDEX idx_is_pinned (is_pinned)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            'news_categories': """
                CREATE TABLE IF NOT EXISTS news_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_name (name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            'news_links': """
                CREATE TABLE IF NOT EXISTS news_links (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    url TEXT NOT NULL,
                    description TEXT,
                    category_id INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES news_categories(id) ON DELETE SET NULL,
                    INDEX idx_created_at (created_at),
                    INDEX idx_category_id (category_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            'projects': """
                CREATE TABLE IF NOT EXISTS projects (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    github_url VARCHAR(500),
                    demo_url VARCHAR(500),
                    technologies TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            'team_members': """
                CREATE TABLE IF NOT EXISTS team_members (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    role VARCHAR(100),
                    bio TEXT,
                    avatar_url VARCHAR(500),
                    github_url VARCHAR(500),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            'learning_resources': """
                CREATE TABLE IF NOT EXISTS learning_resources (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    url VARCHAR(500),
                    type VARCHAR(50),
                    difficulty VARCHAR(20),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_type (type),
                    INDEX idx_difficulty (difficulty)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            'events': """
                CREATE TABLE IF NOT EXISTS events (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    event_date DATE,
                    location VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_event_date (event_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
        }
        
        # 创建表
        for table_name, table_sql in tables.items():
            cursor.execute(table_sql)
            logger.info(f"✅ 表 {table_name} 创建成功")
        
        connection.commit()
        return True
        
    except Error as e:
        logger.error(f"❌ 创建表失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def insert_sample_data():
    """插入示例数据"""
    connection = None
    cursor = None
    try:
        # 连接到指定数据库
        db_config = DB_CONFIG.copy()
        db_config['database'] = DATABASE_NAME
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 插入示例数据
        sample_data = [
            # 公告数据
            """INSERT IGNORE INTO announcements (title, content, is_pinned) VALUES 
               ('欢迎访问NIS社团网站', '这是一个网络信息安全社团的官方网站，提供安全资讯、技术分享和学习资源。', 1),
               ('社团活动通知', '本周五晚上7点将举行CTF训练赛，欢迎大家参加！', 0)""",
            
            # 分类数据
            """INSERT IGNORE INTO news_categories (name, description) VALUES 
               ('漏洞分析', '最新的安全漏洞分析和研究'),
               ('工具介绍', '安全工具的介绍和使用方法'),
               ('技术分享', '网络安全技术文章和经验分享')""",
            
            # 新闻链接数据
            """INSERT IGNORE INTO news_links (title, url, description, category_id) VALUES 
               ('OWASP Top 10', 'https://owasp.org/www-project-top-ten/', 'Web应用安全风险Top 10', 1),
               ('Nmap网络扫描工具', 'https://nmap.org/', '强大的网络发现和安全审计工具', 2)""",
            
            # 项目数据
            """INSERT IGNORE INTO projects (name, description, technologies) VALUES 
               ('安全扫描工具', '自主开发的Web安全扫描工具，支持多种漏洞检测', 'Python, Flask, SQLite'),
               ('CTF平台', '在线CTF竞赛平台，支持多种题型', 'Node.js, React, MySQL')""",
            
            # 团队成员数据
            """INSERT IGNORE INTO team_members (name, role, bio) VALUES 
               ('张三', '社长', '网络安全专业，专注于Web安全研究'),
               ('李四', '技术负责人', '擅长渗透测试和漏洞挖掘')""",
            
            # 学习资源数据
            """INSERT IGNORE INTO learning_resources (title, description, url, type, difficulty) VALUES 
               ('Web安全基础', 'Web安全入门教程，适合初学者', 'https://example.com/web-security', '教程', '初级'),
               ('渗透测试实战', '实际渗透测试案例分析', 'https://example.com/pentest', '案例', '中级')""",
            
            # 活动数据
            """INSERT IGNORE INTO events (title, description, event_date, location) VALUES 
               ('CTF竞赛', '校内CTF网络安全竞赛', '2025-08-01', '计算机学院'),
               ('安全技术讲座', '邀请业界专家分享最新安全技术', '2025-08-15', '学术报告厅')"""
        ]
        
        for data_sql in sample_data:
            cursor.execute(data_sql)
        
        connection.commit()
        logger.info("✅ 示例数据插入成功")
        return True
        
    except Error as e:
        logger.error(f"❌ 插入示例数据失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def main():
    """主函数"""
    logger.info("开始初始化NIS社团网站数据库...")
    
    # 1. 创建数据库
    if not create_database():
        logger.error("数据库创建失败，退出")
        return False
    
    # 2. 创建表
    if not create_tables():
        logger.error("表创建失败，退出")
        return False
    
    # 3. 插入示例数据
    if not insert_sample_data():
        logger.error("示例数据插入失败，退出")
        return False
    
    logger.info("🎉 数据库初始化完成！")
    logger.info(f"数据库名称: {DATABASE_NAME}")
    logger.info("可以启动Flask应用了")
    
    return True

if __name__ == '__main__':
    main()
