# NIS社团网站环境变量配置示例
# 复制此文件为 .env 并填写实际配置值

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=true
SECRET_KEY=your-secret-key-here

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=root
DB_NAME=nis_security_news
DB_PORT=3306

# Firecrawl API配置
FIRECRAWL_API_KEY=fc-8d5914994e864fa0833ecaec769c2688
FIRECRAWL_BASE_URL=https://api.firecrawl.dev/v1

# 爬虫配置
CRAWL_INTERVAL=3600
MAX_ARTICLES_PER_CRAWL=50
ENABLE_AUTO_CRAWL=true

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 安全配置
RATE_LIMIT_DEFAULT=100 per hour
