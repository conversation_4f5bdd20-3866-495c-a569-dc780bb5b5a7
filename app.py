#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - 简化版
专注核心功能：展示页面和管理员登录
"""

import os
import logging
import secrets
import hashlib
import hmac
import sqlite3
from datetime import datetime, timedelta
from functools import wraps
from flask import Flask, request, jsonify, send_from_directory, session
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 安全配置
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_hex(32))
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# 配置CORS
CORS(app, origins=['http://localhost:5000', 'http://127.0.0.1:5000'], supports_credentials=True)

# 数据库配置 - 使用SQLite
DATABASE_PATH = os.getenv('DATABASE_PATH', 'nis_security_news.db')

# 管理员配置
ADMIN_CONFIG = {
    'username': os.getenv('ADMIN_USERNAME', 'admin'),
    'password_hash': hashlib.sha256(os.getenv('ADMIN_PASSWORD', 'nis2024').encode()).hexdigest()
}

def get_db_connection():
    """获取SQLite数据库连接"""
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        connection.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def execute_query(query, params=None, fetch_one=False):
    """执行数据库查询"""
    connection = None
    cursor = None
    try:
        connection = get_db_connection()
        if not connection:
            return None

        cursor = connection.cursor()

        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        if query.strip().upper().startswith('SELECT'):
            if fetch_one:
                result = cursor.fetchone()
                if result:
                    result = dict(result)
            else:
                result = cursor.fetchall()
                if result:
                    result = [dict(row) for row in result]
        else:
            connection.commit()
            result = cursor.rowcount

        return result

    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def generate_csrf_token():
    """生成CSRF令牌"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(32)
    return session['csrf_token']

def validate_csrf_token(token):
    """验证CSRF令牌"""
    return 'csrf_token' in session and hmac.compare_digest(session['csrf_token'], token)

def require_admin_auth(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session or not session['admin_logged_in']:
            return jsonify({'success': False, 'message': '需要管理员权限'}), 401
        return f(*args, **kwargs)
    return decorated_function

def init_database():
    """初始化SQLite数据库"""
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()

        # 创建表
        tables = [
            '''CREATE TABLE IF NOT EXISTS announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                is_pinned INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS news_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS news_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                url TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES news_categories(id)
            )''',
            '''CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                github_url TEXT,
                demo_url TEXT,
                technologies TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )'''
        ]

        for table_sql in tables:
            cursor.execute(table_sql)

        # 插入示例数据（如果表为空）
        cursor.execute("SELECT COUNT(*) FROM announcements")
        if cursor.fetchone()[0] == 0:
            sample_data = [
                "INSERT INTO announcements (title, content, is_pinned) VALUES ('欢迎访问NIS社团网站', '这是一个网络信息安全社团的官方网站，提供安全资讯、技术分享和学习资源。', 1)",
                "INSERT INTO announcements (title, content, is_pinned) VALUES ('社团活动通知', '本周五晚上7点将举行CTF训练赛，欢迎大家参加！', 0)",
                "INSERT INTO news_categories (name, description) VALUES ('漏洞分析', '最新的安全漏洞分析和研究')",
                "INSERT INTO news_categories (name, description) VALUES ('工具介绍', '安全工具的介绍和使用方法')",
                "INSERT INTO news_links (title, url, description, category_id) VALUES ('OWASP Top 10', 'https://owasp.org/www-project-top-ten/', 'Web应用安全风险Top 10', 1)",
                "INSERT INTO projects (name, description, technologies) VALUES ('安全扫描工具', '自主开发的Web安全扫描工具', 'Python, Flask, SQLite')"
            ]

            for data_sql in sample_data:
                cursor.execute(data_sql)

        connection.commit()
        connection.close()
        logger.info("✅ 数据库初始化成功")
        return True

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        return False

# ==================== 静态文件路由 ====================

@app.route('/')
def root():
    return send_from_directory('.', 'index.html')

@app.route('/static/<path:filename>')
def serve_static_files(filename):
    """服务静态文件"""
    try:
        return send_from_directory('static', filename)
    except FileNotFoundError:
        return jsonify({'error': '文件不存在'}), 404

@app.route('/<path:filename>')
def serve_html_files(filename):
    """服务HTML文件"""
    if filename.endswith('.html'):
        try:
            return send_from_directory('.', filename)
        except FileNotFoundError:
            return jsonify({'error': '文件不存在'}), 404
    else:
        return jsonify({'error': '不支持的文件类型'}), 400

# ==================== API路由 ====================

@app.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """获取CSRF令牌"""
    return jsonify({'csrf_token': generate_csrf_token()})

@app.route('/api/announcements', methods=['GET'])
def get_announcements():
    """获取公告列表"""
    try:
        announcements = execute_query("""
            SELECT id, title, content, is_pinned, created_at, updated_at
            FROM announcements 
            ORDER BY is_pinned DESC, created_at DESC
            LIMIT 10
        """)
        
        if not announcements:
            announcements = []
        
        return jsonify({'success': True, 'data': announcements})
    except Exception as e:
        logger.error(f"获取公告失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取公告失败'}), 500

@app.route('/api/news-links', methods=['GET'])
def get_news_links():
    """获取新闻链接"""
    try:
        links = execute_query("""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.created_at,
                   nc.name as category_name
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            ORDER BY nl.created_at DESC
            LIMIT 20
        """)
        
        if not links:
            links = []
        
        return jsonify({'success': True, 'data': links})
    except Exception as e:
        logger.error(f"获取新闻链接失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取新闻链接失败'}), 500

@app.route('/api/projects', methods=['GET'])
def get_projects():
    """获取项目列表"""
    try:
        projects = execute_query("""
            SELECT id, name, description, github_url, demo_url, technologies, created_at
            FROM projects 
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        if not projects:
            projects = []
        
        return jsonify({'success': True, 'data': projects})
    except Exception as e:
        logger.error(f"获取项目失败: {e}")
        return jsonify({'success': False, 'data': [], 'message': '获取项目失败'}), 500

@app.route('/api/admin/login', methods=['POST'])
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400
            
        username = data.get('username', '').strip()
        password = data.get('password', '')
        csrf_token = data.get('csrf_token', '')

        # 验证CSRF令牌
        if not validate_csrf_token(csrf_token):
            return jsonify({'success': False, 'message': 'CSRF令牌无效'}), 403

        # 验证用户名和密码
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        if (username == ADMIN_CONFIG['username'] and 
            hmac.compare_digest(password_hash, ADMIN_CONFIG['password_hash'])):
            
            session['admin_logged_in'] = True
            session['admin_username'] = username
            session['login_time'] = datetime.now().isoformat()
            session.permanent = True
            
            logger.info(f"管理员 {username} 登录成功")
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'csrf_token': generate_csrf_token()
            })
        else:
            logger.warning(f"管理员登录失败，用户名: {username}")
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'message': '登录失败'}), 500

@app.route('/api/admin/logout', methods=['POST'])
@require_admin_auth
def admin_logout():
    """管理员退出登录"""
    try:
        username = session.get('admin_username', 'unknown')
        session.clear()
        logger.info(f"管理员 {username} 退出登录")
        return jsonify({'success': True, 'message': '退出成功'})
    except Exception as e:
        logger.error(f"退出登录失败: {e}")
        return jsonify({'success': False, 'message': '退出失败'}), 500

# ==================== 健康检查 ====================

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        connection = get_db_connection()
        if connection:
            connection.close()
            db_status = "healthy"
        else:
            db_status = "unhealthy"

        return jsonify({
            'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
            'database': db_status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'message': '页面不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"服务器内部错误: {error}")
    return jsonify({'success': False, 'message': '服务器内部错误'}), 500

if __name__ == '__main__':
    logger.info("🚀 NIS社团网站启动中...")

    # 初始化数据库
    if init_database():
        logger.info("💾 数据库初始化完成")
    else:
        logger.error("❌ 数据库初始化失败")
        exit(1)

    logger.info("🌐 访问地址: http://127.0.0.1:5000")
    logger.info("🔧 管理后台: http://127.0.0.1:5000/admin_simple.html")
    logger.info("🔐 管理员账号: admin / nis2024")

    app.run(debug=True, host='127.0.0.1', port=5000)
