#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - 简化版
"""

import os
import logging
import json
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import mysql.connector
from mysql.connector import Error

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'nis-security-club-2024'

# 配置限流器
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)
limiter.init_app(app)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'nis_security_news',
    'charset': 'utf8mb4'
}

# 静态数据（作为备用）
SAMPLE_ANNOUNCEMENTS = [
    {
        'id': 1,
        'title': '欢迎使用NIS社团管理系统',
        'content': '这是一个全新的社团管理系统，包含新闻链接管理、项目展示、学习资源、活动管理等功能。',
        'type': 'notice',
        'status': 'published',
        'is_pinned': True,
        'publish_time': '2024-07-16 10:00:00',
        'created_at': '2024-07-16 10:00:00'
    },
    {
        'id': 2,
        'title': '2024年春季招新开始',
        'content': '我们正在寻找对网络安全感兴趣的同学加入我们的团队！',
        'type': 'event',
        'status': 'published',
        'is_pinned': True,
        'publish_time': '2024-07-16 09:00:00',
        'created_at': '2024-07-16 09:00:00'
    }
]

SAMPLE_NEWS_LINKS = [
    {
        'id': 1,
        'title': 'FreeBuf安全资讯',
        'url': 'https://www.freebuf.com/',
        'description': '国内知名网络安全媒体平台',
        'category': '网络安全',
        'status': 'active',
        'views': 156,
        'is_featured': True,
        'created_at': '2024-07-16 08:00:00'
    },
    {
        'id': 2,
        'title': '安全客',
        'url': 'https://www.anquanke.com/',
        'description': '专业的网络安全技术媒体',
        'category': '网络安全',
        'status': 'active',
        'views': 89,
        'is_featured': True,
        'created_at': '2024-07-16 08:00:00'
    }
]

# 示例项目数据
SAMPLE_PROJECTS = [
    {
        'id': 1,
        'title': '校园网络监控平台',
        'description': '基于Python和ELK Stack的校园网络流量监控与异常检测平台，能够实时监控网络流量并识别潜在的安全威胁。',
        'github_url': 'https://github.com/nis/network-monitor',
        'demo_url': 'http://demo.nis.edu.cn/monitor',
        'tags': ['Python', 'Elasticsearch', 'Flask', 'Docker'],
        'status': '已完成',
        'award': '获2023年四川省大学生网络安全竞赛二等奖',
        'is_featured': True,
        'created_at': '2024-01-15 10:00:00'
    },
    {
        'id': 2,
        'title': 'Web安全扫描器',
        'description': '自主开发的Web应用安全漏洞扫描工具，支持SQL注入、XSS、CSRF等常见漏洞的自动化检测。',
        'github_url': 'https://github.com/nis/web-scanner',
        'demo_url': '',
        'tags': ['Python', 'Requests', 'BeautifulSoup', 'SQLite'],
        'status': '已完成',
        'award': '获校级创新创业大赛一等奖',
        'is_featured': True,
        'created_at': '2024-02-20 14:30:00'
    },
    {
        'id': 3,
        'title': '密码学实验平台',
        'description': '用于密码学教学的在线实验平台，提供各种加密算法的可视化演示和交互式学习体验。',
        'github_url': 'https://github.com/nis/crypto-lab',
        'demo_url': 'http://crypto.nis.edu.cn',
        'tags': ['Vue.js', 'Node.js', 'MySQL', 'WebCrypto'],
        'status': '开发中',
        'award': '',
        'is_featured': False,
        'created_at': '2024-03-10 09:15:00'
    },
    {
        'id': 4,
        'title': '智能防火墙管理系统',
        'description': '基于机器学习的智能防火墙规则管理系统，能够自动分析网络流量并生成最优的防火墙规则。',
        'github_url': 'https://github.com/nis/smart-firewall',
        'demo_url': '',
        'tags': ['Python', 'TensorFlow', 'Django', 'Redis'],
        'status': '规划中',
        'award': '',
        'is_featured': False,
        'created_at': '2024-04-05 16:45:00'
    }
]

# 示例团队成员数据
SAMPLE_TEAM = [
    {
        'id': 1,
        'name': '张三',
        'role': '社团主席',
        'achievement': '计算机科学与技术专业，专注于Web安全和渗透测试，获得多项网络安全竞赛奖项',
        'skills': ['Python', 'Web安全', '渗透测试', 'Linux', 'Docker'],
        'github_url': 'https://github.com/zhangsan',
        'email': '<EMAIL>',
        'join_date': '2022-09-01',
        'is_core': True
    },
    {
        'id': 2,
        'name': '李四',
        'role': '技术副主席',
        'achievement': '网络工程专业，擅长网络安全和系统管理，负责社团技术培训工作',
        'skills': ['网络安全', '系统管理', '防火墙', 'IDS', 'Wireshark'],
        'github_url': 'https://github.com/lisi',
        'email': '<EMAIL>',
        'join_date': '2022-09-01',
        'is_core': True
    },
    {
        'id': 3,
        'name': '王五',
        'role': '项目经理',
        'achievement': '信息安全专业，负责社团项目开发和管理，具有丰富的项目管理经验',
        'skills': ['项目管理', 'Java', '数据库', 'DevOps', 'Git'],
        'github_url': 'https://github.com/wangwu',
        'email': '<EMAIL>',
        'join_date': '2023-03-01',
        'is_core': True
    },
    {
        'id': 4,
        'name': '赵六',
        'role': '研究员',
        'achievement': '信息安全专业，专注于密码学和区块链技术研究，发表多篇学术论文',
        'skills': ['密码学', '区块链', 'C++', '算法设计', '学术研究'],
        'github_url': 'https://github.com/zhaoliu',
        'email': '<EMAIL>',
        'join_date': '2023-09-01',
        'is_core': False
    }
]

# 示例学习资源数据
SAMPLE_RESOURCES = [
    {
        'id': 1,
        'title': '网络安全入门指南',
        'description': '适合初学者的网络安全学习路线图，包含基础知识、工具使用、实战案例等内容',
        'url': 'https://github.com/nis/security-guide',
        'type': 'link',
        'category': '入门教程',
        'difficulty': '初级',
        'tags': ['网络安全', '入门', '学习路线'],
        'download_count': 1250,
        'is_featured': True,
        'created_at': '2024-01-10 08:00:00'
    },
    {
        'id': 2,
        'title': '渗透测试工具集',
        'description': '常用渗透测试工具的使用教程合集，包含Nmap、Metasploit、Burp Suite等工具的详细使用方法',
        'url': '/static/files/pentest-tools.pdf',
        'type': 'file',
        'category': '工具教程',
        'difficulty': '中级',
        'tags': ['渗透测试', '工具', '教程'],
        'download_count': 890,
        'is_featured': True,
        'created_at': '2024-02-15 10:30:00'
    },
    {
        'id': 3,
        'title': '密码学基础视频',
        'description': '密码学基础知识讲解视频，涵盖对称加密、非对称加密、数字签名等核心概念',
        'url': 'https://www.bilibili.com/video/BV1xx411c7mu',
        'type': 'video',
        'category': '理论基础',
        'difficulty': '初级',
        'tags': ['密码学', '视频', '基础'],
        'download_count': 567,
        'is_featured': False,
        'created_at': '2024-03-01 14:20:00'
    },
    {
        'id': 4,
        'title': 'CTF竞赛题库',
        'description': '精选CTF竞赛题目集合，包含Web安全、逆向工程、密码学、取证等多个方向的练习题',
        'url': '/static/files/ctf-challenges.zip',
        'type': 'file',
        'category': '实战练习',
        'difficulty': '高级',
        'tags': ['CTF', '竞赛', '实战'],
        'download_count': 723,
        'is_featured': True,
        'created_at': '2024-03-20 16:45:00'
    },
    {
        'id': 5,
        'title': 'Python安全编程',
        'description': 'Python在网络安全领域的应用教程，包含网络扫描、漏洞利用、自动化工具开发等内容',
        'url': 'https://github.com/nis/python-security',
        'type': 'link',
        'category': '编程教程',
        'difficulty': '中级',
        'tags': ['Python', '安全编程', '自动化'],
        'download_count': 445,
        'is_featured': False,
        'created_at': '2024-04-10 11:15:00'
    }
]

def execute_query(query, params=None, fetch_one=False):
    """执行数据库查询"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            if fetch_one:
                result = cursor.fetchone()
            else:
                result = cursor.fetchall()
        else:
            connection.commit()
            result = cursor.rowcount
        
        cursor.close()
        connection.close()
        return result
        
    except Error as e:
        logger.error(f"数据库操作失败: {e}")
        return None

# ==================== 静态文件路由 ====================

@app.route('/')
def root():
    return send_from_directory('.', 'index.html')

@app.route('/index.html')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/about.html')
def about():
    return send_from_directory('.', 'about.html')

@app.route('/projects.html')
def projects():
    return send_from_directory('.', 'projects.html')

@app.route('/resources.html')
def resources():
    return send_from_directory('.', 'resources.html')

@app.route('/events.html')
def events():
    return send_from_directory('.', 'events.html')

@app.route('/security_news.html')
def security_news():
    return send_from_directory('.', 'security_news.html')

@app.route('/announcements.html')
def announcements():
    return send_from_directory('.', 'announcements.html')

@app.route('/join.html')
def join():
    return send_from_directory('.', 'join.html')

@app.route('/admin_simple.html')
def admin_simple():
    return send_from_directory('.', 'admin_simple.html')

# ==================== API路由 ====================

@app.route('/api/announcements', methods=['GET'])
@limiter.limit("20/minute")
def get_announcements():
    """获取公告列表"""
    try:
        announcements = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            WHERE status = 'published'
            ORDER BY is_pinned DESC, publish_time DESC
        """)
        
        if not announcements:
            announcements = SAMPLE_ANNOUNCEMENTS
        
        return jsonify(announcements)
    except Exception as e:
        logger.error(f"获取公告失败: {e}")
        return jsonify(SAMPLE_ANNOUNCEMENTS)

@app.route('/api/announcements/<int:announcement_id>', methods=['GET'])
@limiter.limit("30/minute")
def get_announcement_detail(announcement_id):
    """获取公告详情"""
    try:
        announcement = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            WHERE id = %s AND status = 'published'
        """, (announcement_id,), fetch_one=True)
        
        if not announcement:
            return jsonify({'error': '公告不存在'}), 404
        
        return jsonify(announcement)
    except Exception as e:
        logger.error(f"获取公告详情失败: {e}")
        return jsonify({'error': '获取公告详情失败'}), 500

@app.route('/api/news-links', methods=['GET'])
@limiter.limit("20/minute")
def get_news_links():
    """获取新闻链接列表"""
    try:
        links = execute_query("""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE nl.status = 'active'
            ORDER BY nl.is_featured DESC, nl.created_at DESC
        """)
        
        if not links:
            links = SAMPLE_NEWS_LINKS
        
        return jsonify(links)
    except Exception as e:
        logger.error(f"获取新闻链接失败: {e}")
        return jsonify(SAMPLE_NEWS_LINKS)

@app.route('/api/categories', methods=['GET'])
@limiter.limit("20/minute")
def get_categories():
    """获取新闻分类"""
    try:
        categories = execute_query("""
            SELECT id, name, description, sort_order
            FROM news_categories
            WHERE is_active = 1
            ORDER BY sort_order
        """)

        if not categories:
            categories = [
                {'id': 1, 'name': '网络安全', 'description': '网络安全相关新闻'},
                {'id': 2, 'name': '漏洞情报', 'description': '安全漏洞和威胁情报'},
                {'id': 3, 'name': '技术分析', 'description': '安全技术分析文章'},
                {'id': 4, 'name': '行业动态', 'description': '安全行业动态新闻'}
            ]

        return jsonify(categories)
    except Exception as e:
        logger.error(f"获取分类失败: {e}")
        return jsonify([])

@app.route('/api/security-news', methods=['GET'])
@limiter.limit("20/minute")
def get_security_news():
    """获取安全新闻列表（兼容前端调用）"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        category = request.args.get('category', 'all')
        search = request.args.get('search', '')

        # 构建查询条件
        where_conditions = ["nl.status = 'active'"]
        params = []

        if category != 'all':
            where_conditions.append("nc.name = %s")
            params.append(category)

        if search:
            where_conditions.append("(nl.title LIKE %s OR nl.description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)
        offset = (page - 1) * limit

        # 查询新闻链接
        query = f"""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE {where_clause}
            ORDER BY nl.is_featured DESC, nl.created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([limit, offset])

        links = execute_query(query, params)

        if not links:
            links = SAMPLE_NEWS_LINKS

        # 检查是否还有更多数据
        count_query = f"""
            SELECT COUNT(*) as total
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE {where_clause}
        """
        count_result = execute_query(count_query, params[:-2], fetch_one=True)
        total = count_result['total'] if count_result else len(links)
        has_more = (page * limit) < total

        return jsonify({
            'news': links,
            'has_more': has_more,
            'total': total,
            'page': page
        })
    except Exception as e:
        logger.error(f"获取安全新闻失败: {e}")
        return jsonify({
            'news': SAMPLE_NEWS_LINKS,
            'has_more': False,
            'total': len(SAMPLE_NEWS_LINKS),
            'page': 1
        })

@app.route('/api/news-links/<int:link_id>/view', methods=['POST'])
@limiter.limit("10/minute")
def increment_view_count(link_id):
    """增加链接浏览次数"""
    try:
        result = execute_query("""
            UPDATE news_links
            SET views = views + 1
            WHERE id = %s
        """, (link_id,))

        if result:
            return jsonify({'success': True, 'message': '浏览次数已更新'})
        else:
            return jsonify({'success': False, 'message': '链接不存在'}), 404
    except Exception as e:
        logger.error(f"更新浏览次数失败: {e}")
        return jsonify({'success': False, 'message': '更新失败'}), 500

@app.route('/api/projects', methods=['GET'])
@limiter.limit("20/minute")
def get_projects():
    """获取项目列表"""
    try:
        projects = execute_query("""
            SELECT id, title, description, github_url, demo_url, technologies as tags,
                   status, award, is_featured, created_at
            FROM projects
            WHERE status != 'archived'
            ORDER BY is_featured DESC, created_at DESC
        """)

        if not projects:
            projects = SAMPLE_PROJECTS
        else:
            # 处理JSON字段
            for project in projects:
                if project.get('tags') and isinstance(project['tags'], str):
                    try:
                        project['tags'] = json.loads(project['tags'])
                    except:
                        project['tags'] = []

        return jsonify(projects)
    except Exception as e:
        logger.error(f"获取项目失败: {e}")
        return jsonify(SAMPLE_PROJECTS)

@app.route('/api/team', methods=['GET'])
@limiter.limit("20/minute")
def get_team():
    """获取团队成员"""
    try:
        team = execute_query("""
            SELECT id, name, position as role, bio as achievement, skills,
                   github_url, email, join_date, is_core
            FROM team_members
            WHERE is_active = 1
            ORDER BY is_core DESC, sort_order ASC
        """)

        if not team:
            team = SAMPLE_TEAM
        else:
            # 处理JSON字段
            for member in team:
                if member.get('skills') and isinstance(member['skills'], str):
                    try:
                        member['skills'] = json.loads(member['skills'])
                    except:
                        member['skills'] = []

        return jsonify(team)
    except Exception as e:
        logger.error(f"获取团队信息失败: {e}")
        return jsonify(SAMPLE_TEAM)

@app.route('/api/resources', methods=['GET'])
@limiter.limit("20/minute")
def get_resources():
    """获取学习资源"""
    try:
        resources = execute_query("""
            SELECT id, title, description, url, resource_type as type,
                   category, difficulty, tags, download_count, is_featured, created_at
            FROM learning_resources
            WHERE status = 'active'
            ORDER BY is_featured DESC, created_at DESC
        """)

        if not resources:
            resources = SAMPLE_RESOURCES
        else:
            # 处理JSON字段
            for resource in resources:
                if resource.get('tags') and isinstance(resource['tags'], str):
                    try:
                        resource['tags'] = json.loads(resource['tags'])
                    except:
                        resource['tags'] = []

        return jsonify(resources)
    except Exception as e:
        logger.error(f"获取学习资源失败: {e}")
        return jsonify(SAMPLE_RESOURCES)

@app.route('/api/events', methods=['GET'])
@limiter.limit("20/minute")
def get_events():
    """获取活动列表"""
    try:
        events = execute_query("""
            SELECT id, title, description, content, event_date, location,
                   image_url, participants_count, status, is_featured, created_at
            FROM events
            ORDER BY event_date DESC
        """)

        if not events:
            events = [
                {
                    'id': 1,
                    'title': '2024年网络安全技能大赛',
                    'description': '社团年度网络安全技能竞赛活动',
                    'content': '本次大赛包含Web安全、逆向工程、密码学等多个方向的挑战题目，旨在提升成员的实战技能。',
                    'event_date': '2024-03-15',
                    'location': '学校机房A301',
                    'image_url': '/static/images/event1.jpg',
                    'participants_count': 45,
                    'status': 'completed',
                    'is_featured': True,
                    'created_at': '2024-03-01 10:00:00'
                },
                {
                    'id': 2,
                    'title': '企业安全专家讲座',
                    'description': '邀请知名企业安全专家分享实战经验',
                    'content': '本次讲座邀请了腾讯安全团队的资深专家，分享企业级安全防护的最佳实践。',
                    'event_date': '2024-04-20',
                    'location': '学术报告厅',
                    'image_url': '/static/images/event2.jpg',
                    'participants_count': 120,
                    'status': 'completed',
                    'is_featured': True,
                    'created_at': '2024-04-01 14:30:00'
                }
            ]

        return jsonify(events)
    except Exception as e:
        logger.error(f"获取活动失败: {e}")
        return jsonify([])

# ==================== 管理员API ====================

@app.route('/api/admin/login', methods=['POST'])
@limiter.limit("5/minute")
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if username == 'admin' and password == 'nis2024':
            return jsonify({
                'success': True,
                'message': '登录成功',
                'token': 'admin-token-2024'
            })
        else:
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'message': '登录失败'}), 500

@app.route('/api/admin/announcements', methods=['GET'])
@limiter.limit("20/minute")
def admin_get_announcements():
    """管理员获取所有公告"""
    try:
        announcements = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            ORDER BY is_pinned DESC, created_at DESC
        """)

        if not announcements:
            announcements = SAMPLE_ANNOUNCEMENTS

        return jsonify(announcements)
    except Exception as e:
        logger.error(f"获取公告失败: {e}")
        return jsonify(SAMPLE_ANNOUNCEMENTS)

@app.route('/api/admin/announcements', methods=['POST'])
@limiter.limit("10/minute")
def admin_create_announcement():
    """创建公告"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['title', 'content', 'type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400

        # 插入数据库
        result = execute_query("""
            INSERT INTO announcements (title, content, type, status, is_pinned, publish_time, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            data['title'],
            data['content'],
            data['type'],
            data.get('status', 'published'),
            data.get('is_pinned', False),
            data.get('publish_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))

        if result:
            return jsonify({'success': True, 'message': '公告创建成功'})
        else:
            return jsonify({'success': False, 'message': '创建失败'}), 500

    except Exception as e:
        logger.error(f"创建公告失败: {e}")
        return jsonify({'success': False, 'message': '创建失败'}), 500

@app.route('/api/admin/announcements/<int:announcement_id>', methods=['PUT'])
@limiter.limit("10/minute")
def admin_update_announcement(announcement_id):
    """更新公告"""
    try:
        data = request.get_json()

        # 构建更新字段
        update_fields = []
        params = []

        for field in ['title', 'content', 'type', 'status', 'is_pinned', 'publish_time']:
            if field in data:
                update_fields.append(f"{field} = %s")
                params.append(data[field])

        if not update_fields:
            return jsonify({'success': False, 'message': '没有要更新的字段'}), 400

        update_fields.append("updated_at = %s")
        params.append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        params.append(announcement_id)

        query = f"UPDATE announcements SET {', '.join(update_fields)} WHERE id = %s"
        result = execute_query(query, params)

        if result:
            return jsonify({'success': True, 'message': '公告更新成功'})
        else:
            return jsonify({'success': False, 'message': '公告不存在'}), 404

    except Exception as e:
        logger.error(f"更新公告失败: {e}")
        return jsonify({'success': False, 'message': '更新失败'}), 500

@app.route('/api/admin/announcements/<int:announcement_id>', methods=['DELETE'])
@limiter.limit("10/minute")
def admin_delete_announcement(announcement_id):
    """删除公告"""
    try:
        result = execute_query("DELETE FROM announcements WHERE id = %s", (announcement_id,))

        if result:
            return jsonify({'success': True, 'message': '公告删除成功'})
        else:
            return jsonify({'success': False, 'message': '公告不存在'}), 404

    except Exception as e:
        logger.error(f"删除公告失败: {e}")
        return jsonify({'success': False, 'message': '删除失败'}), 500

@app.route('/api/admin/news-links', methods=['GET'])
@limiter.limit("20/minute")
def admin_get_news_links():
    """管理员获取所有新闻链接"""
    try:
        links = execute_query("""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at, nl.updated_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            ORDER BY nl.created_at DESC
        """)

        if not links:
            links = SAMPLE_NEWS_LINKS

        return jsonify(links)
    except Exception as e:
        logger.error(f"获取新闻链接失败: {e}")
        return jsonify(SAMPLE_NEWS_LINKS)

@app.route('/api/admin/news-links', methods=['POST'])
@limiter.limit("10/minute")
def admin_create_news_link():
    """创建新闻链接"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['title', 'url', 'description']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400

        # 获取分类ID
        category_id = 1  # 默认分类
        if data.get('category'):
            category_result = execute_query(
                "SELECT id FROM news_categories WHERE name = %s",
                (data['category'],),
                fetch_one=True
            )
            if category_result:
                category_id = category_result['id']

        # 插入数据库
        result = execute_query("""
            INSERT INTO news_links (title, url, description, category_id, status, is_featured, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            data['title'],
            data['url'],
            data['description'],
            category_id,
            data.get('status', 'active'),
            data.get('is_featured', False),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))

        if result:
            return jsonify({'success': True, 'message': '链接创建成功'})
        else:
            return jsonify({'success': False, 'message': '创建失败'}), 500

    except Exception as e:
        logger.error(f"创建链接失败: {e}")
        return jsonify({'success': False, 'message': '创建失败'}), 500

@app.route('/api/admin/news-links/<int:link_id>', methods=['DELETE'])
@limiter.limit("10/minute")
def admin_delete_news_link(link_id):
    """删除新闻链接"""
    try:
        result = execute_query("DELETE FROM news_links WHERE id = %s", (link_id,))

        if result:
            return jsonify({'success': True, 'message': '链接删除成功'})
        else:
            return jsonify({'success': False, 'message': '链接不存在'}), 404

    except Exception as e:
        logger.error(f"删除链接失败: {e}")
        return jsonify({'success': False, 'message': '删除失败'}), 500

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': '页面不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({'error': '请求过于频繁，请稍后再试'}), 429

# ==================== 启动应用 ====================

if __name__ == '__main__':
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    logger.info("🚀 NIS社团网站启动中...")
    logger.info("📊 简化版管理系统")
    logger.info("🌐 访问地址: http://127.0.0.1:5000")
    logger.info("🔧 管理后台: http://127.0.0.1:5000/admin_simple.html")
    logger.info("🔐 管理员账号: admin / nis2024")
    
    app.run(debug=True, host='127.0.0.1', port=5000)
