#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - SQLite数据库初始化脚本
"""

import sqlite3
import logging
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

DATABASE_PATH = 'nis_security_news.db'

def create_tables():
    """创建数据表"""
    connection = None
    cursor = None
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        # 创建表的SQL语句
        tables = [
            '''CREATE TABLE IF NOT EXISTS announcements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                is_pinned INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS news_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS news_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                url TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES news_categories(id)
            )''',
            '''CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                github_url TEXT,
                demo_url TEXT,
                technologies TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS team_members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                role TEXT,
                bio TEXT,
                avatar_url TEXT,
                github_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS learning_resources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                url TEXT,
                type TEXT,
                difficulty TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''',
            '''CREATE TABLE IF NOT EXISTS events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                event_date DATE,
                location TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )'''
        ]
        
        # 创建表
        for table_sql in tables:
            cursor.execute(table_sql)
            logger.info("✅ 表创建成功")
        
        connection.commit()
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def insert_sample_data():
    """插入示例数据"""
    connection = None
    cursor = None
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        # 插入示例数据
        sample_data = [
            # 公告数据
            "INSERT OR IGNORE INTO announcements (title, content, is_pinned) VALUES ('欢迎访问NIS社团网站', '这是一个网络信息安全社团的官方网站，提供安全资讯、技术分享和学习资源。', 1)",
            "INSERT OR IGNORE INTO announcements (title, content, is_pinned) VALUES ('社团活动通知', '本周五晚上7点将举行CTF训练赛，欢迎大家参加！', 0)",
            
            # 分类数据
            "INSERT OR IGNORE INTO news_categories (name, description) VALUES ('漏洞分析', '最新的安全漏洞分析和研究')",
            "INSERT OR IGNORE INTO news_categories (name, description) VALUES ('工具介绍', '安全工具的介绍和使用方法')",
            "INSERT OR IGNORE INTO news_categories (name, description) VALUES ('技术分享', '网络安全技术文章和经验分享')",
            
            # 新闻链接数据
            "INSERT OR IGNORE INTO news_links (title, url, description, category_id) VALUES ('OWASP Top 10', 'https://owasp.org/www-project-top-ten/', 'Web应用安全风险Top 10', 1)",
            "INSERT OR IGNORE INTO news_links (title, url, description, category_id) VALUES ('Nmap网络扫描工具', 'https://nmap.org/', '强大的网络发现和安全审计工具', 2)",
            
            # 项目数据
            "INSERT OR IGNORE INTO projects (name, description, technologies) VALUES ('安全扫描工具', '自主开发的Web安全扫描工具，支持多种漏洞检测', 'Python, Flask, SQLite')",
            "INSERT OR IGNORE INTO projects (name, description, technologies) VALUES ('CTF平台', '在线CTF竞赛平台，支持多种题型', 'Node.js, React, MySQL')",
            
            # 团队成员数据
            "INSERT OR IGNORE INTO team_members (name, role, bio) VALUES ('张三', '社长', '网络安全专业，专注于Web安全研究')",
            "INSERT OR IGNORE INTO team_members (name, role, bio) VALUES ('李四', '技术负责人', '擅长渗透测试和漏洞挖掘')",
            
            # 学习资源数据
            "INSERT OR IGNORE INTO learning_resources (title, description, url, type, difficulty) VALUES ('Web安全基础', 'Web安全入门教程，适合初学者', 'https://example.com/web-security', '教程', '初级')",
            "INSERT OR IGNORE INTO learning_resources (title, description, url, type, difficulty) VALUES ('渗透测试实战', '实际渗透测试案例分析', 'https://example.com/pentest', '案例', '中级')",
            
            # 活动数据
            "INSERT OR IGNORE INTO events (title, description, event_date, location) VALUES ('CTF竞赛', '校内CTF网络安全竞赛', '2025-08-01', '计算机学院')",
            "INSERT OR IGNORE INTO events (title, description, event_date, location) VALUES ('安全技术讲座', '邀请业界专家分享最新安全技术', '2025-08-15', '学术报告厅')"
        ]
        
        for data_sql in sample_data:
            cursor.execute(data_sql)
        
        connection.commit()
        logger.info("✅ 示例数据插入成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 插入示例数据失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def main():
    """主函数"""
    logger.info("开始初始化NIS社团网站SQLite数据库...")
    
    # 删除旧数据库文件（如果存在）
    if os.path.exists(DATABASE_PATH):
        os.remove(DATABASE_PATH)
        logger.info("删除旧数据库文件")
    
    # 1. 创建表
    if not create_tables():
        logger.error("表创建失败，退出")
        return False
    
    # 2. 插入示例数据
    if not insert_sample_data():
        logger.error("示例数据插入失败，退出")
        return False
    
    logger.info("🎉 SQLite数据库初始化完成！")
    logger.info(f"数据库文件: {DATABASE_PATH}")
    logger.info("可以启动Flask应用了")
    
    return True

if __name__ == '__main__':
    main()
