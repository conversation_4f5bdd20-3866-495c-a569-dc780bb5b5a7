// NIS安全社团网站 - 改进版公共JavaScript函数

// 全局配置
const CONFIG = {
    API_BASE_URL: '',
    ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 300,
    NOTIFICATION_DURATION: 5000,
    CACHE_DURATION: 5 * 60 * 1000, // 5分钟
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000
};

// 缓存管理
class CacheManager {
    constructor() {
        this.cache = new Map();
        this.timestamps = new Map();
    }

    set(key, value, ttl = CONFIG.CACHE_DURATION) {
        this.cache.set(key, value);
        this.timestamps.set(key, Date.now() + ttl);
    }

    get(key) {
        if (!this.cache.has(key)) return null;
        
        const expiry = this.timestamps.get(key);
        if (Date.now() > expiry) {
            this.cache.delete(key);
            this.timestamps.delete(key);
            return null;
        }
        
        return this.cache.get(key);
    }

    clear() {
        this.cache.clear();
        this.timestamps.clear();
    }

    delete(key) {
        this.cache.delete(key);
        this.timestamps.delete(key);
    }
}

// 全局缓存实例
const cache = new CacheManager();

// 工具函数
const utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化日期
    formatDate(dateString, options = {}) {
        const date = new Date(dateString);
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleDateString('zh-CN', { ...defaultOptions, ...options });
    },

    // 格式化相对时间
    formatRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const minute = 60 * 1000;
        const hour = minute * 60;
        const day = hour * 24;
        const week = day * 7;
        const month = day * 30;
        const year = day * 365;

        if (diff < minute) return '刚刚';
        if (diff < hour) return `${Math.floor(diff / minute)}分钟前`;
        if (diff < day) return `${Math.floor(diff / hour)}小时前`;
        if (diff < week) return `${Math.floor(diff / day)}天前`;
        if (diff < month) return `${Math.floor(diff / week)}周前`;
        if (diff < year) return `${Math.floor(diff / month)}个月前`;
        return `${Math.floor(diff / year)}年前`;
    },

    // HTML转义
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    },

    // 截断文本
    truncateText(text, maxLength, suffix = '...') {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength - suffix.length) + suffix;
    },

    // 生成随机ID
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    // 深拷贝
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    // 检查元素是否在视口中
    isInViewport(element, threshold = 0) {
        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        const windowWidth = window.innerWidth || document.documentElement.clientWidth;
        
        return (
            rect.top >= -threshold &&
            rect.left >= -threshold &&
            rect.bottom <= windowHeight + threshold &&
            rect.right <= windowWidth + threshold
        );
    },

    // 平滑滚动到元素
    scrollToElement(element, offset = 0, duration = 800) {
        const targetPosition = element.offsetTop - offset;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = ease(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }

        function ease(t, b, c, d) {
            t /= d / 2;
            if (t < 1) return c / 2 * t * t + b;
            t--;
            return -c / 2 * (t * (t - 2) - 1) + b;
        }

        requestAnimationFrame(animation);
    },

    // 检查设备类型
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 检查是否支持触摸
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    // 获取URL参数
    getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        const result = {};
        for (const [key, value] of params) {
            result[key] = value;
        }
        return result;
    },

    // 设置URL参数
    setUrlParam(key, value) {
        const url = new URL(window.location);
        url.searchParams.set(key, value);
        window.history.replaceState({}, '', url);
    }
};

// HTTP请求管理
class HttpClient {
    constructor() {
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
    }

    async request(url, options = {}) {
        const config = {
            method: 'GET',
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        // 添加CSRF令牌
        if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(config.method.toUpperCase())) {
            const csrfToken = await this.getCsrfToken();
            if (csrfToken) {
                config.headers['X-CSRF-Token'] = csrfToken;
                if (config.body && typeof config.body === 'object') {
                    config.body.csrf_token = csrfToken;
                }
            }
        }

        // 处理请求体
        if (config.body && typeof config.body === 'object' && config.headers['Content-Type'] === 'application/json') {
            config.body = JSON.stringify(config.body);
        }

        let retries = 0;
        while (retries < CONFIG.MAX_RETRIES) {
            try {
                const response = await fetch(CONFIG.API_BASE_URL + url, config);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                }
                return await response.text();
            } catch (error) {
                retries++;
                if (retries >= CONFIG.MAX_RETRIES) {
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY * retries));
            }
        }
    }

    async get(url, options = {}) {
        // 检查缓存
        const cacheKey = `GET:${url}`;
        const cached = cache.get(cacheKey);
        if (cached && !options.noCache) {
            return cached;
        }

        const result = await this.request(url, { ...options, method: 'GET' });
        
        // 缓存GET请求结果
        if (!options.noCache) {
            cache.set(cacheKey, result);
        }
        
        return result;
    }

    async post(url, data, options = {}) {
        return this.request(url, { ...options, method: 'POST', body: data });
    }

    async put(url, data, options = {}) {
        return this.request(url, { ...options, method: 'PUT', body: data });
    }

    async delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }

    async getCsrfToken() {
        try {
            const response = await this.request('/api/csrf-token', { method: 'GET' });
            return response.csrf_token;
        } catch (error) {
            console.warn('获取CSRF令牌失败:', error);
            return null;
        }
    }
}

// 全局HTTP客户端实例
const http = new HttpClient();

// 通知系统
class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.init();
    }

    init() {
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
    }

    show(message, type = 'info', duration = CONFIG.NOTIFICATION_DURATION) {
        const id = utils.generateId('notification');
        const notification = document.createElement('div');
        
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: var(--dark-card);
            color: var(--light-text);
            padding: 16px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid ${this.getTypeColor(type)};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            pointer-events: auto;
            max-width: 400px;
            word-wrap: break-word;
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 18px;">${this.getTypeIcon(type)}</span>
                <span style="flex: 1;">${utils.escapeHtml(message)}</span>
                <button onclick="notifications.hide('${id}')" style="
                    background: none;
                    border: none;
                    color: var(--gray-text);
                    cursor: pointer;
                    font-size: 18px;
                    padding: 0;
                    margin-left: 10px;
                ">&times;</button>
            </div>
        `;

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // 触发动画
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => this.hide(id), duration);
        }

        return id;
    }

    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }

    getTypeColor(type) {
        const colors = {
            success: '#48bb78',
            error: '#f56565',
            warning: '#ed8936',
            info: '#4299e1'
        };
        return colors[type] || colors.info;
    }

    getTypeIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }
}

// 全局通知管理器实例
const notifications = new NotificationManager();

// 加载状态管理
class LoadingManager {
    constructor() {
        this.loadingElements = new Map();
    }

    show(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (!element) return;

        const loadingId = utils.generateId('loading');
        const originalContent = element.innerHTML;
        
        this.loadingElements.set(loadingId, {
            element,
            originalContent
        });

        element.innerHTML = `
            <div class="loading-spinner" style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 40px 20px;
                color: var(--gray-text);
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 3px solid var(--dark-border);
                    border-top: 3px solid var(--primary-color);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-bottom: 16px;
                "></div>
                <p>${utils.escapeHtml(text)}</p>
            </div>
        `;

        return loadingId;
    }

    hide(loadingId) {
        const loadingData = this.loadingElements.get(loadingId);
        if (!loadingData) return;

        const { element, originalContent } = loadingData;
        element.innerHTML = originalContent;
        this.loadingElements.delete(loadingId);
    }

    showGlobal(text = '加载中...') {
        const overlay = document.createElement('div');
        overlay.id = 'global-loading';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 14, 26, 0.8);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
        `;
        
        overlay.innerHTML = `
            <div style="
                background: var(--dark-card);
                padding: 40px;
                border-radius: 12px;
                border: 1px solid var(--dark-border);
                text-align: center;
                color: var(--light-text);
            ">
                <div style="
                    width: 50px;
                    height: 50px;
                    border: 4px solid var(--dark-border);
                    border-top: 4px solid var(--primary-color);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 20px;
                "></div>
                <p style="margin: 0; font-size: 16px;">${utils.escapeHtml(text)}</p>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    hideGlobal() {
        const overlay = document.getElementById('global-loading');
        if (overlay) {
            overlay.remove();
        }
    }
}

// 全局加载管理器实例
const loading = new LoadingManager();

// 表单验证
class FormValidator {
    constructor() {
        this.rules = {
            required: (value) => value.trim() !== '',
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
            phone: (value) => /^1[3-9]\d{9}$/.test(value),
            url: (value) => /^https?:\/\/.+/.test(value),
            minLength: (value, min) => value.length >= min,
            maxLength: (value, max) => value.length <= max,
            pattern: (value, pattern) => new RegExp(pattern).test(value)
        };

        this.messages = {
            required: '此字段为必填项',
            email: '请输入有效的邮箱地址',
            phone: '请输入有效的手机号码',
            url: '请输入有效的URL地址',
            minLength: '长度不能少于 {min} 个字符',
            maxLength: '长度不能超过 {max} 个字符',
            pattern: '格式不正确'
        };
    }

    validate(form) {
        const errors = {};
        const formData = new FormData(form);
        
        for (const [name, value] of formData) {
            const field = form.querySelector(`[name="${name}"]`);
            if (!field) continue;

            const rules = this.parseRules(field);
            const fieldErrors = [];

            for (const rule of rules) {
                if (!this.validateRule(value, rule)) {
                    fieldErrors.push(this.formatMessage(rule));
                }
            }

            if (fieldErrors.length > 0) {
                errors[name] = fieldErrors;
            }
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    parseRules(field) {
        const rules = [];
        
        if (field.hasAttribute('required')) {
            rules.push({ type: 'required' });
        }
        
        if (field.type === 'email') {
            rules.push({ type: 'email' });
        }
        
        if (field.hasAttribute('data-phone')) {
            rules.push({ type: 'phone' });
        }
        
        if (field.type === 'url') {
            rules.push({ type: 'url' });
        }
        
        if (field.hasAttribute('minlength')) {
            rules.push({ type: 'minLength', value: parseInt(field.getAttribute('minlength')) });
        }
        
        if (field.hasAttribute('maxlength')) {
            rules.push({ type: 'maxLength', value: parseInt(field.getAttribute('maxlength')) });
        }
        
        if (field.hasAttribute('pattern')) {
            rules.push({ type: 'pattern', value: field.getAttribute('pattern') });
        }

        return rules;
    }

    validateRule(value, rule) {
        const validator = this.rules[rule.type];
        if (!validator) return true;
        
        return rule.value !== undefined ? validator(value, rule.value) : validator(value);
    }

    formatMessage(rule) {
        let message = this.messages[rule.type] || '验证失败';
        if (rule.value !== undefined) {
            message = message.replace(`{${rule.type.replace(/[A-Z]/g, letter => letter.toLowerCase())}}`, rule.value);
        }
        return message;
    }

    showErrors(form, errors) {
        // 清除之前的错误
        form.querySelectorAll('.field-error').forEach(error => error.remove());
        form.querySelectorAll('.error').forEach(field => field.classList.remove('error'));

        // 显示新错误
        for (const [fieldName, fieldErrors] of Object.entries(errors)) {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (!field) continue;

            field.classList.add('error');
            
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.style.cssText = `
                color: var(--error-color);
                font-size: 14px;
                margin-top: 4px;
            `;
            errorElement.textContent = fieldErrors[0];
            
            field.parentNode.appendChild(errorElement);
        }
    }
}

// 全局表单验证器实例
const validator = new FormValidator();

// 导航栏功能
function initNavigation() {
    const navbar = document.querySelector('.navbar');
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    // 滚动效果
    const handleScroll = utils.throttle(() => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }, 100);
    
    window.addEventListener('scroll', handleScroll);
    
    // 移动端菜单切换
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });
        
        // 点击菜单项关闭菜单
        navMenu.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            });
        });
    }
    
    // 高亮当前页面
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const currentLink = document.querySelector(`.nav-link[href="${currentPage}"]`);
    if (currentLink) {
        document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
        currentLink.classList.add('active');
    }
}

// 滚动效果
function initScrollEffects() {
    // 返回顶部按钮
    const backToTop = document.getElementById('back-to-top');
    if (backToTop) {
        const handleScroll = utils.throttle(() => {
            if (window.scrollY > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }, 100);
        
        window.addEventListener('scroll', handleScroll);
        
        backToTop.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
    
    // 平滑滚动锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                utils.scrollToElement(target, 80);
            }
        });
    });
}

// 返回顶部功能
function initBackToTop() {
    const backToTop = document.getElementById('back-to-top');
    if (!backToTop) return;

    const handleScroll = utils.throttle(() => {
        if (window.scrollY > 300) {
            backToTop.classList.add('visible');
        } else {
            backToTop.classList.remove('visible');
        }
    }, 100);

    window.addEventListener('scroll', handleScroll);
    
    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

// 显示空状态
function showEmptyState(containerId, message = '暂无数据') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = `
        <div class="empty-state" style="
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-text);
        ">
            <div style="font-size: 48px; margin-bottom: 16px;">📭</div>
            <p style="font-size: 16px; margin: 0;">${utils.escapeHtml(message)}</p>
        </div>
    `;
}

// 显示错误状态
function showErrorState(containerId, message = '加载失败') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = `
        <div class="error-state" style="
            text-align: center;
            padding: 60px 20px;
            color: var(--error-color);
        ">
            <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
            <p style="font-size: 16px; margin: 0 0 16px;">${utils.escapeHtml(message)}</p>
            <button onclick="location.reload()" style="
                background: var(--primary-color);
                color: var(--light-text);
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
            ">重试</button>
        </div>
    `;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error input, .error textarea, .error select {
            border-color: var(--error-color) !important;
            box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1) !important;
        }
    `;
    document.head.appendChild(style);
    
    // 初始化基础功能
    initNavigation();
    initScrollEffects();
    initBackToTop();
    
    console.log('NIS安全社团网站已加载完成');
});

// 导出到全局
window.utils = utils;
window.http = http;
window.notifications = notifications;
window.loading = loading;
window.validator = validator;
window.cache = cache;
window.CONFIG = CONFIG;

