@echo off
echo ========================================
echo NIS网络信息安全社团网站启动脚本
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import flask, flask_cors" >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装依赖包...
    pip install flask flask-cors python-dotenv
)

echo.
echo 启动应用...
echo 访问地址: http://127.0.0.1:5000
echo 管理后台: http://127.0.0.1:5000/admin_simple.html
echo 管理员账号: admin / nis2024
echo.
echo 按 Ctrl+C 停止服务器
echo.

python app.py

pause
