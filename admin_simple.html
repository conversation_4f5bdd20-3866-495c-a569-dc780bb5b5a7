<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员后台 - NIS网络信息安全社团</title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .admin-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .nav-tab {
            transition: all 0.3s ease;
            border-radius: 10px;
        }
        
        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <!-- 头部 -->
        <div class="admin-card p-6 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">
                        <i class="fas fa-shield-alt mr-3 text-blue-600"></i>
                        NIS社团管理后台
                    </h1>
                    <p class="text-gray-600 mt-2">网络信息安全社团 - 简化管理系统</p>
                </div>
                <div class="flex space-x-4">
                    <a href="/index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-home mr-2"></i>返回首页
                    </a>
                    <button id="logout-btn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                    </button>
                </div>
            </div>
        </div>

        <!-- 登录表单 -->
        <div id="login-section" class="admin-card p-8 max-w-md mx-auto">
            <h2 class="text-2xl font-bold text-center mb-6">管理员登录</h2>
            <form id="login-form">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <input type="text" id="username" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <input type="password" id="password" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg font-semibold">
                    <i class="fas fa-sign-in-alt mr-2"></i>登录
                </button>
            </form>
            <div class="mt-4 text-center text-sm text-gray-600">
                默认账号：admin / nis2024
            </div>
        </div>

        <!-- 管理界面 -->
        <div id="admin-interface" style="display: none;">
            <!-- 导航标签 -->
            <div class="admin-card p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <button class="nav-tab active px-6 py-3 font-semibold" data-tab="announcements">
                        <i class="fas fa-bullhorn mr-2"></i>公告管理
                    </button>
                    <button class="nav-tab px-6 py-3 font-semibold" data-tab="news-links">
                        <i class="fas fa-link mr-2"></i>安全资讯管理
                    </button>
                    <button class="nav-tab px-6 py-3 font-semibold" data-tab="system">
                        <i class="fas fa-cog mr-2"></i>系统设置
                    </button>
                </div>
            </div>

            <!-- 公告管理 -->
            <div id="announcements-section" class="content-section active">
                <div class="admin-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-bullhorn mr-3 text-blue-600"></i>公告管理
                        </h2>
                        <button id="add-announcement-btn" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold">
                            <i class="fas fa-plus mr-2"></i>添加公告
                        </button>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布时间</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="announcements-table" class="bg-white divide-y divide-gray-200">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 安全资讯管理 -->
            <div id="news-links-section" class="content-section">
                <div class="admin-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-link mr-3 text-blue-600"></i>安全资讯管理
                        </h2>
                        <button id="add-link-btn" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold">
                            <i class="fas fa-plus mr-2"></i>添加资讯链接
                        </button>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">浏览量</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="links-table" class="bg-white divide-y divide-gray-200">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div id="system-section" class="content-section">
                <div class="admin-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-cog mr-3 text-blue-600"></i>系统设置
                        </h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 网站信息 -->
                        <div class="bg-white p-6 rounded-lg shadow">
                            <h3 class="text-lg font-semibold mb-4">网站信息</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">网站名称</label>
                                    <input type="text" id="site-name" value="NIS网络信息安全社团" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">管理员邮箱</label>
                                    <input type="email" id="admin-email" value="<EMAIL>" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                        </div>

                        <!-- 联系方式 -->
                        <div class="bg-white p-6 rounded-lg shadow">
                            <h3 class="text-lg font-semibold mb-4">联系方式</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">QQ群</label>
                                    <input type="text" id="contact-qq" value="242050951" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">抖音号</label>
                                    <input type="text" id="contact-douyin" value="21647629167" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button id="save-settings-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold">
                            <i class="fas fa-save mr-2"></i>保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框和脚本将在下一部分添加 -->
    <script>
        // 简化的管理员脚本
        let isLoggedIn = false;
        let currentTab = 'announcements';
        let editingId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            bindEvents();
            checkLoginStatus();
        });

        function bindEvents() {
            // 登录表单
            document.getElementById('login-form').addEventListener('submit', handleLogin);

            // 退出登录
            document.getElementById('logout-btn').addEventListener('click', handleLogout);

            // 标签切换
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const tabName = e.target.closest('.nav-tab').dataset.tab;
                    switchTab(tabName);
                });
            });

            // 添加公告按钮
            const addAnnouncementBtn = document.getElementById('add-announcement-btn');
            if (addAnnouncementBtn) {
                addAnnouncementBtn.addEventListener('click', showAnnouncementModal);
            }

            // 添加链接按钮
            const addLinkBtn = document.getElementById('add-link-btn');
            if (addLinkBtn) {
                addLinkBtn.addEventListener('click', showLinkModal);
            }

            // 保存设置按钮
            const saveSettingsBtn = document.getElementById('save-settings-btn');
            if (saveSettingsBtn) {
                saveSettingsBtn.addEventListener('click', saveSettings);
            }
        }

        function handleLogin(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'nis2024') {
                isLoggedIn = true;
                localStorage.setItem('adminLoggedIn', 'true');
                showAdminInterface();
                loadData();
            } else {
                alert('用户名或密码错误！');
            }
        }

        function handleLogout() {
            isLoggedIn = false;
            localStorage.removeItem('adminLoggedIn');
            showLoginForm();
        }

        function checkLoginStatus() {
            if (localStorage.getItem('adminLoggedIn') === 'true') {
                isLoggedIn = true;
                showAdminInterface();
                loadData();
            } else {
                showLoginForm();
            }
        }

        function showLoginForm() {
            document.getElementById('login-section').style.display = 'block';
            document.getElementById('admin-interface').style.display = 'none';
        }

        function showAdminInterface() {
            document.getElementById('login-section').style.display = 'none';
            document.getElementById('admin-interface').style.display = 'block';
        }

        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            
            // 更新内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(`${tabName}-section`).classList.add('active');
            
            currentTab = tabName;
            loadData();
        }

        function loadData() {
            if (currentTab === 'announcements') {
                loadAnnouncements();
            } else if (currentTab === 'news-links') {
                loadNewsLinks();
            }
        }

        async function loadAnnouncements() {
            try {
                const response = await fetch('/api/admin/announcements');
                const announcements = await response.json();

                const tableBody = document.getElementById('announcements-table');
                tableBody.innerHTML = '';

                announcements.forEach(announcement => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${announcement.title}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${announcement.type}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${announcement.status}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(announcement.created_at).toLocaleDateString()}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editAnnouncement(${announcement.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                            <button onclick="deleteAnnouncement(${announcement.id})" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('加载公告失败:', error);
                showMessage('加载公告失败', 'error');
            }
        }

        async function loadNewsLinks() {
            try {
                const response = await fetch('/api/admin/news-links');
                const links = await response.json();

                const tableBody = document.getElementById('links-table');
                tableBody.innerHTML = '';

                links.forEach(link => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${link.title}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${link.category || '未分类'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${link.views || 0}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${link.status}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editLink(${link.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                            <button onclick="deleteLink(${link.id})" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('加载链接失败:', error);
                showMessage('加载链接失败', 'error');
            }
        }

        function showAnnouncementModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">添加公告</h3>
                    </div>
                    <div class="p-6">
                        <form id="announcement-form">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                                <input type="text" id="announcement-title" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">内容</label>
                                <textarea id="announcement-content" required rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg"></textarea>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">类型</label>
                                <select id="announcement-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="notice">通知</option>
                                    <option value="event">活动</option>
                                    <option value="news">新闻</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="announcement-pinned" class="mr-2">
                                    <span class="text-sm text-gray-700">置顶显示</span>
                                </label>
                            </div>
                            <div class="flex justify-end space-x-4">
                                <button type="button" onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
                                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">保存</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 绑定表单提交事件
            document.getElementById('announcement-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await saveAnnouncement();
                modal.remove();
            });
        }

        function showLinkModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">添加安全资讯链接</h3>
                    </div>
                    <div class="p-6">
                        <form id="link-form">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                                <input type="text" id="link-title" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">链接地址</label>
                                <input type="url" id="link-url" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                <textarea id="link-description" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg"></textarea>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">分类</label>
                                <select id="link-category" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="网络安全">网络安全</option>
                                    <option value="漏洞情报">漏洞情报</option>
                                    <option value="技术分析">技术分析</option>
                                    <option value="行业动态">行业动态</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="link-featured" class="mr-2">
                                    <span class="text-sm text-gray-700">推荐链接</span>
                                </label>
                            </div>
                            <div class="flex justify-end space-x-4">
                                <button type="button" onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
                                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">保存</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 绑定表单提交事件
            document.getElementById('link-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await saveLink();
                modal.remove();
            });
        }

        async function saveAnnouncement() {
            try {
                const data = {
                    title: document.getElementById('announcement-title').value,
                    content: document.getElementById('announcement-content').value,
                    type: document.getElementById('announcement-type').value,
                    is_pinned: document.getElementById('announcement-pinned').checked
                };

                const response = await fetch('/api/admin/announcements', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('公告创建成功', 'success');
                    loadAnnouncements();
                } else {
                    showMessage(result.message || '创建失败', 'error');
                }
            } catch (error) {
                console.error('保存公告失败:', error);
                showMessage('保存失败', 'error');
            }
        }

        async function saveLink() {
            try {
                const data = {
                    title: document.getElementById('link-title').value,
                    url: document.getElementById('link-url').value,
                    description: document.getElementById('link-description').value,
                    category: document.getElementById('link-category').value,
                    is_featured: document.getElementById('link-featured').checked
                };

                const response = await fetch('/api/admin/news-links', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('链接创建成功', 'success');
                    loadNewsLinks();
                } else {
                    showMessage(result.message || '创建失败', 'error');
                }
            } catch (error) {
                console.error('保存链接失败:', error);
                showMessage('保存失败', 'error');
            }
        }

        async function deleteAnnouncement(id) {
            if (!confirm('确定要删除这个公告吗？')) return;

            try {
                const response = await fetch(`/api/admin/announcements/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('公告删除成功', 'success');
                    loadAnnouncements();
                } else {
                    showMessage(result.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除公告失败:', error);
                showMessage('删除失败', 'error');
            }
        }

        async function deleteLink(id) {
            if (!confirm('确定要删除这个链接吗？')) return;

            try {
                const response = await fetch(`/api/admin/news-links/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('链接删除成功', 'success');
                    loadNewsLinks();
                } else {
                    showMessage(result.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除链接失败:', error);
                showMessage('删除失败', 'error');
            }
        }

        function editAnnouncement(id) {
            showMessage(`编辑公告功能开发中... ID: ${id}`, 'info');
        }

        function editLink(id) {
            showMessage(`编辑链接功能开发中... ID: ${id}`, 'info');
        }

        function saveSettings() {
            showMessage('设置保存功能开发中...', 'info');
        }

        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            }`;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>
